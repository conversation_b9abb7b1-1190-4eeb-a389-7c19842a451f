server:
  port: 9999
spring:
    application:
      name: user-service
    cloud:
      nacos:
        discovery:
          server-addr: localhost:8848
    redis:
      host: ***************:6379
      database: 0
    datasource:
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ***********************************************************************************************************
      username: root
      password: root


# 添加 Seata 配置
seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: my_test_tx_group
  enable-auto-data-source-proxy: false
  service:
    vgroup-mapping:
      my_test_tx_group: default
    grouplist:
      default: 127.0.0.1:8091
  config:
    type: file
  registry:
    type: file


