package com.xinhe.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinhe.custom.CustomUserDetails;
import com.xinhe.mapper.TUserMapper;
import com.xinhe.pojo.TUser;
import com.xinhe.service.TUserService;
import org.checkerframework.checker.units.qual.Acceleration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Service
public class TUserServiceImpl extends ServiceImpl<TUserMapper, TUser> implements TUserService,UserDetailsService {

    private static final Logger log = LoggerFactory.getLogger(TUserServiceImpl.class);

    @Autowired
    private TUserMapper tUserMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        TUser tUser = tUserMapper.selectOne(new QueryWrapper<TUser>().eq("username", username));
        if (tUser != null) {
            return new CustomUserDetails(tUser,new ArrayList<>());
        }else {
            throw new UsernameNotFoundException("用户不存在");
        }
//        return null;
    }
}
