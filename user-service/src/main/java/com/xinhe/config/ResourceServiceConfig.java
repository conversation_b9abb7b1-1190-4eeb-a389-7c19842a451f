package com.xinhe.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.token.RemoteTokenServices;

/**
 * <AUTHOR>
 * 资源服务器配置
 * 实现 JWT 验证与权限控制
 */
@Configuration
// 开启资源服务器
@EnableResourceServer
// 开启方法级别权限控制
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class ResourceServiceConfig extends ResourceServerConfigurerAdapter {

    private static final String RESOURCE_ID = "resource-server";

    @Override
    public void configure(HttpSecurity http) throws Exception {
        // 配置资源访问权限
        http.authorizeRequests().antMatchers("/api/public/**").permitAll()
                // 私有资源需要认证
                .antMatchers("/api/private/**").authenticated();
    }

    @Override
    public void configure(ResourceServerSecurityConfigurer resources) throws Exception {
        resources
                // 设置资源ID
                .resourceId(RESOURCE_ID)
                // 设置令牌服务
                .tokenServices(tokenServices());
    }
    // 自定义令牌服务
    public RemoteTokenServices tokenServices() {
        RemoteTokenServices tokenServices = new RemoteTokenServices();
        // 设置令牌校验端点
        tokenServices.setCheckTokenEndpointUrl("http://localhost:8081/oauth/check_token");
        // 设置客户端ID
        tokenServices.setClientId("client");
        // 设置客户端密码
        tokenServices.setClientSecret("client_secret");
        return tokenServices;
    }
}
