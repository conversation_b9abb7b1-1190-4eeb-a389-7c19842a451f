package com.xinhe.config;

import com.xinhe.custom.CustomTokenEnhancer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.client.JdbcClientDetailsService;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenEnhancerChain;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;

import javax.sql.DataSource;
import java.util.Arrays;

/**
 * 生产环境认证服务器配置类
 * 负责OAuth2认证服务器的核心配置，包括客户端信息、令牌存储、JWT配置等
 */
@Configuration
@EnableAuthorizationServer
public class AuthorizationServerConfig extends AuthorizationServerConfigurerAdapter {

    // 认证管理器
    @Autowired
    private AuthenticationManager authenticationManager;

    // Redis连接工厂
    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    // 数据源，用于从数据库读取客户端信息
    @Autowired
    private DataSource dataSource;

    // 用户详情服务
    @Autowired
    private UserDetailsService userDetailsService;

    /**
     * 客户端信息服务，生产环境从数据库读取客户端配置
     */
    @Bean
    public ClientDetailsService clientDetailsService() {
        return new JdbcClientDetailsService(dataSource);
    }

    /**
     * 令牌存储配置，使用Redis存储令牌，支持分布式环境
     */
    @Bean
    public TokenStore tokenStore() {
        RedisTokenStore tokenStore = new RedisTokenStore(redisConnectionFactory);
        tokenStore.setPrefix("prod:oauth:token:");  // Redis键前缀，避免键冲突
        return tokenStore;
    }

    /**
     * JWT令牌转换器，用于生成和验证JWT令牌
     */
    @Bean
    public JwtAccessTokenConverter jwtAccessTokenConverter() {
        JwtAccessTokenConverter converter = new JwtAccessTokenConverter();
        // 从Nacos配置中心获取JWT签名密钥，生产环境不硬编码密钥
        converter.setSigningKey(nacosConfigService.getConfig("jwt.signing.key", "DEFAULT_GROUP", 5000));
        return converter;
    }

    /**
     * 令牌增强器，添加额外信息到JWT令牌
     */
    @Bean
    public TokenEnhancer tokenEnhancer() {
        return new CustomTokenEnhancer();
    }

    /**
     * 配置客户端详情
     */
    @Override
    public void configure(ClientDetailsServiceConfigurer clients) throws Exception {
        // 生产环境使用数据库存储客户端信息
        clients.withClientDetails(clientDetailsService());
    }

    /**
     * 配置令牌端点
     */
    @Override
    public void configure(AuthorizationServerEndpointsConfigurer endpoints) throws Exception {
        TokenEnhancerChain enhancerChain = new TokenEnhancerChain();
        enhancerChain.setTokenEnhancers(Arrays.asList(tokenEnhancer(), jwtAccessTokenConverter()));

        endpoints
                // 认证管理器
                .authenticationManager(authenticationManager)
                // 用户详情服务
                .userDetailsService(userDetailsService)
                // 令牌存储
                .tokenStore(tokenStore())
                // JWT转换器
                .accessTokenConverter(jwtAccessTokenConverter())
                // 令牌增强器
                .tokenEnhancer(enhancerChain)
                // 禁止刷新令牌重用
                .reuseRefreshTokens(false);
    }

    /**
     * 配置令牌端点安全策略
     */@Override
    public void configure(AuthorizationServerSecurityConfigurer security) throws Exception {
        security
                .tokenKeyAccess("isAuthenticated()")  // 获取公钥需要认证
                .checkTokenAccess("isAuthenticated()") // 验证令牌需要认证
                .allowFormAuthenticationForClients();  // 允许客户端表单认证
    }
}
